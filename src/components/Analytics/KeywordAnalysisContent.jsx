import React, { useState, useEffect } from "react";
import { Search, Plus, RefreshCw, Trash2, TrendingUp } from "lucide-react";
import { useKeywordAutoRefresh } from "../../hooks/useAutoRefresh";

const KeywordAnalysisContent = () => {
  const [addedPlugins, setAddedPlugins] = useState([]);
  const [keywords, setKeywords] = useState([]);
  const [selectedPlugin, setSelectedPlugin] = useState("");
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedKeywords, setSelectedKeywords] = useState(new Set());
  const [activeTab, setActiveTab] = useState("performance");

  // Load added plugins
  const loadAddedPlugins = async () => {
    try {
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(
        `${BASE_URL}/api/plugins/rank/all?limit=1000`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.plugins && Array.isArray(data.plugins)) {
        setAddedPlugins(data.plugins);
      } else {
        console.warn("Invalid plugins data received:", data);
        setAddedPlugins([]);
      }
    } catch (error) {
      console.error("Error loading added plugins:", error);
      setAddedPlugins([]);
      window.toast("Failed to load plugins", "error");
    }
  };

  // Load keywords for selected plugin (or all keywords if no plugin selected)
  const loadKeywords = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";

      // Build URL with optional pluginSlug parameter
      const url = selectedPlugin
        ? `${BASE_URL}/api/keywords?pluginSlug=${selectedPlugin}`
        : `${BASE_URL}/api/keywords`;

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      if (data.success) {
        // Keywords now come with rank data already included
        const processedKeywords = data.keywords.map((keyword) => ({
          ...keyword,
          position: keyword.latestRank,
          lastChecked:
            keyword.rankHistory && keyword.rankHistory.length > 0
              ? keyword.rankHistory[0].fetchedAt
              : null,
        }));

        setKeywords(processedKeywords);
      }
    } catch (error) {
      console.error("Error loading keywords:", error);
      window.toast("Failed to load keywords", "error");
    } finally {
      setLoading(false);
    }
  };

  // Refresh all keyword ranks
  const handleRefreshRanks = async () => {
    try {
      setRefreshing(true);
      window.toast("Refreshing keyword ranks...", "info");

      const token = localStorage.getItem("adminToken");
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(`${BASE_URL}/api/keywords/refresh-ranks`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (response.status === 429) {
        // Handle concurrent refresh error
        window.toast(data.message || "Refresh already in progress", "warning");
        return;
      }

      if (data.success) {
        const successMessage =
          data.errors && data.errors.length > 0
            ? `${data.message} (${data.errors.length} errors occurred)`
            : data.message;
        window.toast(successMessage, "success");
        // Reload keywords to show updated data
        await loadKeywords();
      } else {
        window.toast(
          data.message || "Failed to refresh keyword ranks",
          "error"
        );
      }
    } catch (error) {
      console.error("Error refreshing ranks:", error);
      window.toast("Failed to refresh keyword ranks", "error");
    } finally {
      setRefreshing(false);
    }
  };

  // Auto-refresh keyword ranks at 9:15 AM GMT+6
  useKeywordAutoRefresh(handleRefreshRanks, true);

  useEffect(() => {
    loadAddedPlugins();
  }, []);

  useEffect(() => {
    loadKeywords();
  }, [selectedPlugin]);

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("performance")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "performance"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            <TrendingUp className="h-4 w-4 inline mr-2" />
            Keyword Performance
          </button>
        </nav>
      </div>

      {/* Keyword Performance Tab */}
      {activeTab === "performance" && (
        <>
          {/* Filters */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex justify-between items-center gap-3">
              {/* Filter by Plugin */}
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                  Plugin:
                </label>
                <select
                  value={selectedPlugin}
                  onChange={(e) => setSelectedPlugin(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All plugins</option>
                  {Array.isArray(addedPlugins) &&
                    addedPlugins.map((plugin) => (
                      <option key={plugin.pluginSlug} value={plugin.pluginSlug}>
                        {plugin.displayName}
                      </option>
                    ))}
                </select>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={handleRefreshRanks}
                  disabled={refreshing}
                  className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm"
                >
                  <RefreshCw
                    className={`h-4 w-4 mr-1 ${
                      refreshing ? "animate-spin" : ""
                    }`}
                  />
                  {refreshing ? "Refreshing..." : "Refresh Ranks"}
                </button>
              </div>
            </div>
          </div>

          {/* Keywords Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-base font-semibold text-gray-900">
                Keywords
                {selectedPlugin && (
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    for{" "}
                    {Array.isArray(addedPlugins)
                      ? addedPlugins.find(
                          (p) => p.pluginSlug === selectedPlugin
                        )?.displayName
                      : selectedPlugin}
                  </span>
                )}
              </h3>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <RefreshCw className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-3" />
                <p className="text-gray-600">Loading keywords...</p>
              </div>
            ) : keywords.length > 0 ? (
              <>
                <div className="overflow-x-auto max-h-[470px] overflow-y-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50 sticky top-0 z-10">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                          Keyword
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Position
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Tracked
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Updated
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {keywords.map((keyword, index) => (
                        <tr
                          key={keyword._id}
                          className={
                            index % 2 === 0 ? "bg-white" : "bg-gray-50"
                          }
                        >
                          <td className="px-4 py-3 whitespace-nowrap w-1/2">
                            <div className="text-sm font-medium text-gray-900">
                              {keyword.keyword}
                            </div>
                            <div className="text-xs text-gray-500">
                              {keyword.pluginName || keyword.pluginSlug}
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6">
                            {keyword.position || "-"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6">
                            {keyword.isActive ? "Yes" : "No"}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6">
                            {(() => {
                              const dateToUse =
                                keyword.lastChecked || keyword.updatedAt;
                              if (!dateToUse) return "-";

                              const date = new Date(dateToUse);
                              // Check if date is valid
                              if (isNaN(date.getTime())) {
                                return "-";
                              }

                              return date.toLocaleDateString("en-GB");
                            })()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="p-8 text-center">
                <Search className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  No Keywords Found
                </h4>
                <p className="text-gray-600 mb-4">
                  {selectedPlugin
                    ? "No keywords added for this plugin yet."
                    : "No keywords added yet. Go to the full Keyword Analysis page to add keywords."}
                </p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default KeywordAnalysisContent;
